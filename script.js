// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Force scroll to top on page load
    window.scrollTo(0, 0);
    
    // Prevent scroll restoration
    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }
    
    console.log('DOM loaded, initializing pricing toggles...');
    
    // Initial check of toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-option');
    console.log('Found toggle buttons:', toggleButtons.length);
    
    // Set initial annual state without triggering events
    const annualToggle = document.querySelector('.toggle-option[data-period="annual"]');
    const monthlyToggle = document.querySelector('.toggle-option[data-period="monthly"]');
    
    if (annualToggle && monthlyToggle) {
        console.log('Setting up initial toggle state...');
        annualToggle.classList.add('active');
        monthlyToggle.classList.remove('active');
        
        // Set initial prices to annual values
        updatePricing('starter', 'annual');
        updatePricing('growth', 'annual');
        updatePricing('enterprise', 'annual');
        
        // Set initial starter plan button state
        updateStarterPlanButton('annual');
    }
    
    // Log initial state of pricing elements
    document.querySelectorAll('.pricing-card').forEach(card => {
        const plan = card.querySelector('h3').textContent.toLowerCase();
        const discountPrice = card.querySelector('.price .discount-price');
        console.log('Initial pricing state:', {
            plan,
            discountPrice: discountPrice ? {
                element: discountPrice,
                monthly: discountPrice.getAttribute('data-monthly'),
                annual: discountPrice.getAttribute('data-annual'),
                currentText: discountPrice.textContent
            } : 'not found'
        });
    });
    
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);
    
    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.slide-in-left, .slide-in-right, .slide-in-up');
    animatedElements.forEach(function(element) {
        observer.observe(element);
    });
    
    // Header scroll effect with collapsing functionality
    let lastScrollTop = 0;
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Collapsing header logic
        if (scrollTop > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Add enhanced visual effects for scrolled state
        if (scrollTop > 100) {
            header.style.boxShadow = '0 4px 20px rgba(0, 168, 255, 0.2)';
            header.style.borderBottom = '1px solid rgba(0, 168, 255, 0.3)';
        } else {
            header.style.boxShadow = 'none';
            header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Smooth scrolling for internal links
    function smoothScroll(target) {
        const element = document.querySelector(target);
        if (element) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = element.offsetTop - headerHeight - 20;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }
    
    // Handle navigation menu clicks
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('href');
            smoothScroll(target);
            
            // Track navigation clicks
            trackEvent('nav_click', {
                section: target.replace('#', ''),
                link_text: this.textContent.trim()
            });
        });
    });
    
    // CTA Button click handlers
    const ctaButtons = document.querySelectorAll('.cta-button');
    ctaButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            // Special handling for starter plan button
            if (this.classList.contains('starter-plan-btn')) {
                // Check if this is a monthly subscription (has actual Stripe URL)
                if (this.href && this.href !== '#' && this.href !== window.location.href + '#') {
                    // Allow navigation to Stripe for monthly subscription in same tab
                    console.log('Navigating to Stripe for monthly subscription:', this.href);
                    trackEvent('stripe_redirect', {
                        plan: 'starter',
                        period: 'monthly',
                        url: this.href
                    });
                    // Let the default link behavior handle navigation
                    return;
                } else {
                    // For annual plan, scroll to pricing section (free trial flow)
                    e.preventDefault();
                    const pricingSection = document.querySelector('#pricing');
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    const targetPosition = pricingSection.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
                return;
            }
            
            // Handle "Learn More" buttons the same way as navigation links
            if (this.textContent.trim() === "Learn More") {
                e.preventDefault();
                smoothScroll('#benefits');
                
                // Track Learn More clicks
                trackEvent('learn_more_click', {
                    button_location: this.closest('header') ? 'header' : 'hero'
                });
                return;
            }
            
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // If this is a pricing card "Contact Sales" button, show the booking modal
            if (button.textContent === "Contact Sales") {
                showBookingModal();
            } else {
                // For all other buttons, scroll to pricing section
                const pricingSection = document.querySelector('#pricing');
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = pricingSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            // Toggle mobile menu (you can expand this for a full mobile menu)
            console.log('Mobile menu clicked');
        });
    }
    
    // Pricing card hover effects
    const pricingCards = document.querySelectorAll('.pricing-card');
    pricingCards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            if (this.classList.contains('featured')) {
                this.style.transform = 'scale(1.05)';
            } else {
                this.style.transform = '';
            }
        });
    });
    
    // Feature cards stagger animation
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(function(card, index) {
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Testimonials rotation (optional enhancement)
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    let currentTestimonial = 0;
    
    function rotateTestimonials() {
        // This could be expanded to create a carousel effect
        testimonialCards.forEach(function(card, index) {
            if (index === currentTestimonial) {
                card.style.opacity = '1';
                card.style.transform = 'scale(1.05)';
            } else {
                card.style.opacity = '0.8';
                card.style.transform = 'scale(1)';
            }
        });
        
        currentTestimonial = (currentTestimonial + 1) % testimonialCards.length;
    }
    
    // Rotate testimonials every 5 seconds
    if (testimonialCards.length > 0) {
        setInterval(rotateTestimonials, 5000);
    }
    
    // Form validation and submission (for future contact forms)
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Demo booking modal (placeholder implementation)
    function showBookingModal() {
        // Create modal overlay
        const modalOverlay = document.createElement('div');
        modalOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            padding: 3rem;
            border-radius: 1rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        `;
        
        modalContent.innerHTML = `
            <div style="color: #6366f1; font-size: 3rem; margin-bottom: 1rem;">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <h2 style="color: #1f2937; margin-bottom: 1rem; font-size: 1.5rem;">Book Your Demo</h2>
            <p style="color: #6b7280; margin-bottom: 2rem; line-height: 1.6;">
                Ready to see AlertIQ in action? Our team will show you exactly how to protect your clients' Amazon listings and save hours of manual monitoring.
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button onclick="window.open('https://calendly.com', '_blank')" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 2rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: transform 0.2s ease;
                " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    Schedule Demo
                </button>
                <button onclick="this.closest('[style*=fixed]').remove()" style="
                    background: #f3f4f6;
                    color: #6b7280;
                    border: none;
                    padding: 0.75rem 2rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                " onmouseover="this.style.background='#e5e7eb'" onmouseout="this.style.background='#f3f4f6'">
                    Maybe Later
                </button>
            </div>
        `;
        
        modalOverlay.appendChild(modalContent);
        document.body.appendChild(modalOverlay);
        
        // Animate modal in
        setTimeout(() => {
            modalOverlay.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        }, 10);
        
        // Close modal when clicking overlay
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                modalOverlay.remove();
            }
        });
        
        // Close modal with escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                modalOverlay.remove();
            }
        });
    }
    
    // Performance optimization: Debounce scroll events
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Lazy loading for images (when you add real screenshots)
    function lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
    
    // Analytics tracking (placeholder for when you integrate analytics)
    function trackEvent(eventName, properties = {}) {
        // Example: Google Analytics 4 event tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, properties);
        }
        
        // Example: Facebook Pixel tracking
        if (typeof fbq !== 'undefined') {
            fbq('track', eventName, properties);
        }
        
        console.log('Event tracked:', eventName, properties);
    }
    
    // Track CTA clicks
    ctaButtons.forEach(function(button, index) {
        button.addEventListener('click', function() {
            trackEvent('cta_click', {
                button_text: this.textContent.trim(),
                button_position: index,
                page_section: this.closest('section')?.className || 'unknown'
            });
        });
    });
    
    // Track scroll depth
    let maxScrollDepth = 0;
    const scrollDepthTracking = debounce(function() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = Math.round((scrollTop / docHeight) * 100);
        
        if (scrollPercent > maxScrollDepth) {
            maxScrollDepth = scrollPercent;
            
            // Track milestone percentages
            if ([25, 50, 75, 90].includes(scrollPercent)) {
                trackEvent('scroll_depth', {
                    depth_percent: scrollPercent
                });
            }
        }
    }, 500);
    
    window.addEventListener('scroll', scrollDepthTracking);
    
    // Initialize lazy loading
    lazyLoadImages();
    
    // FAQ Accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(function(item) {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        question.addEventListener('click', function() {
            const isActive = item.classList.contains('active');
            
            // Close all other FAQ items
            faqItems.forEach(function(otherItem) {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });
            
            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
            } else {
                item.classList.add('active');
            }
            
            // Track FAQ interaction
            trackEvent('faq_click', {
                question: question.querySelector('h4').textContent.trim(),
                expanded: !isActive
            });
        });
    });
    
    // Add loading states for dynamic content
    function showLoading(element) {
        element.style.opacity = '0.6';
        element.style.pointerEvents = 'none';
    }
    
    function hideLoading(element) {
        element.style.opacity = '1';
        element.style.pointerEvents = 'auto';
    }
    
    // Page visibility API for tracking engagement
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            trackEvent('page_hidden');
        } else {
            trackEvent('page_visible');
        }
    });
    
    // Console welcome message for developers
    console.log('%c🚀 AlertIQ Landing Page Loaded Successfully!', 'color: #6366f1; font-size: 16px; font-weight: bold;');
    console.log('%cBuilt with vanilla HTML, CSS, and JavaScript for optimal performance', 'color: #10b981; font-size: 12px;');

    // Pricing Toggle Functionality

    const toggleOptions = document.querySelectorAll('.toggle-option');
    const toggleNote = document.querySelector('.toggle-note');

    toggleOptions.forEach(option => {
        option.addEventListener('click', function() {
            const period = this.dataset.period;
            console.log('Toggle clicked:', period);
            
            // Prevent re-triggering if already active
            if (this.classList.contains('active')) {
                console.log('Toggle already active, skipping update');
                return;
            }
            
            // Update active state
            toggleOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            
            console.log('Updating prices to:', period);
            console.log('Current Elements State:');
            document.querySelectorAll('.pricing-card').forEach(card => {
                const discountPrice = card.querySelector('.price .discount-price');
                console.log({
                    plan: card.querySelector('h3').textContent,
                    discountPrice: discountPrice ? {
                        current: discountPrice.textContent,
                        monthly: discountPrice.getAttribute('data-monthly'),
                        annual: discountPrice.getAttribute('data-annual')
                    } : 'not found'
                });
            });
            
            // Update pricing for each plan
            updatePricing('starter', period);
            updatePricing('growth', period);
            updatePricing('enterprise', period);
            
            // Update starter plan button URL
            updateStarterPlanButton(period);
            
            // Update toggle note
            toggleNote.textContent = period === 'annual' ? 'Monthly rates when billed annually' : 'Get 2 months FREE with annual billing!';
            
            // Track toggle interaction
            trackEvent('pricing_toggle', {
                period: period,
                from: period === 'annual' ? 'monthly' : 'annual'
            });
        });
    });

    function updatePricing(plan, period) {
        // Find pricing card by searching through all cards
        let targetCard = null;
        document.querySelectorAll('.pricing-card').forEach(card => {
            const planName = card.querySelector('h3').textContent.toLowerCase();
            if (planName === plan.toLowerCase()) {
                targetCard = card;
            }
        });
        
        if (targetCard) {
            const amountElem = targetCard.querySelector('.amount');
            const billingNoteElem = targetCard.querySelector('.billing-note');
            
            if (amountElem) {
                const newPrice = amountElem.getAttribute(`data-${period}`);
                console.log(`Updating ${plan} to ${period}: ${newPrice}`);
                amountElem.textContent = newPrice;
                
                // Show/hide billing note based on period
                if (billingNoteElem) {
                    if (period === 'annual') {
                        billingNoteElem.style.display = 'block';
                        billingNoteElem.textContent = '(Billed annually, includes two free months)';
                    } else {
                        billingNoteElem.style.display = 'none';
                    }
                }
                
                // Add smooth transition effect
                targetCard.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    if (targetCard.classList.contains('featured')) {
                        targetCard.style.transform = 'scale(1.05)';
                    } else {
                        targetCard.style.transform = '';
                    }
                }, 150);
            }
        } else {
            console.error(`Could not find pricing card for plan: ${plan}`);
        }
    }

    function updateStarterPlanButton(period) {
        const starterBtn = document.querySelector('.starter-plan-btn');
        if (starterBtn) {
            if (period === 'monthly') {
                // For monthly, use Stripe payment link but keep same text
                const monthlyUrl = starterBtn.getAttribute('data-monthly-url');
                starterBtn.href = monthlyUrl;
                starterBtn.textContent = 'Start Free Trial';
                console.log('Updated starter button to monthly Stripe link:', monthlyUrl);
            } else {
                // For annual, use free trial (prevent default navigation)
                starterBtn.href = '#';
                starterBtn.textContent = 'Start Free Trial';
                console.log('Updated starter button to annual free trial');
            }
            
            // Track button update
            trackEvent('starter_button_update', {
                period: period,
                button_text: starterBtn.textContent,
                href: starterBtn.href
            });
        }
    }
});

// Utility functions available globally
window.AlertIQ = {
    // Smooth scroll to any element
    scrollTo: function(target) {
        const element = document.querySelector(target);
        if (element) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = element.offsetTop - headerHeight - 20;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 300px;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(400px)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
};
