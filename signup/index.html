<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Your Free Trial - CloseIQ</title>
    <link rel="icon" type="image/png" href="../public/favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Colors from main site */
            --primary-color: #00a8ff;
            --primary-dark: #0084c7;
            --secondary-color: #e100ff;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --bg-dark: #050914;
            --bg-darker: #030712;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #00a8ff 0%, #e100ff 100%);
            
            /* Typography */
            --font-family: 'Inter', sans-serif;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            
            /* Spacing */
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Border Radius */
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            
            /* Shadows */
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--white);
            background: 
                radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
                radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
                var(--bg-dark);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        /* Header */
        .header {
            background: var(--bg-dark);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--space-4) 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo img {
            height: 40px;
            width: auto;
            max-width: 200px;
            object-fit: contain;
        }

        .back-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--white);
        }

        /* Main Content */
        .main-content {
            padding: var(--space-20) 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 100px);
        }

        .page-header {
            text-align: center;
            margin-bottom: var(--space-12);
        }

        .page-title {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            margin-bottom: var(--space-4);
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: var(--font-size-xl);
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: var(--space-8);
        }

        .trust-indicators {
            display: flex;
            justify-content: center;
            gap: var(--space-8);
            flex-wrap: wrap;
            color: rgba(255, 255, 255, 0.6);
            font-size: var(--font-size-sm);
        }

        .trust-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        /* Step Indicator */
        .step-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--space-12);
            gap: var(--space-4);
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-2);
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: var(--gradient-primary);
            color: var(--white);
            box-shadow: 0 0 20px rgba(0, 168, 255, 0.5);
        }

        .step-label {
            font-size: var(--font-size-sm);
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
            text-align: center;
        }

        .step.active .step-label {
            color: var(--white);
            font-weight: 600;
        }

        .step-connector {
            width: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 1px;
        }

        /* Payment Container */
        .payment-container {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        .payment-container::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius-xl);
            z-index: -1;
            padding: 2px;
        }

        .auto-popup-status {
            text-align: center;
            padding: var(--space-8);
            color: var(--text-dark);
        }

        .popup-icon {
            font-size: var(--font-size-3xl);
            color: var(--primary-color);
            margin-bottom: var(--space-4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
        }

        .popup-title {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: var(--space-3);
        }

        .popup-description {
            color: var(--text-light);
            margin-bottom: var(--space-6);
            line-height: 1.6;
        }

        .manual-button {
            background: var(--gradient-primary);
            color: var(--white);
            border: none;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            font-size: var(--font-size-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            margin-bottom: var(--space-4);
        }

        .manual-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .fallback-text {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            font-style: italic;
        }

        .stripe-checkout {
            width: 100%;
            min-height: 500px;
            border-radius: var(--border-radius-lg);
        }

        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 600px;
            color: var(--text-light);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--gray-200);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: var(--space-4);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-state {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 600px;
            color: var(--text-light);
            text-align: center;
        }

        .error-icon {
            font-size: var(--font-size-3xl);
            color: #ef4444;
            margin-bottom: var(--space-4);
        }

        .retry-button {
            background: var(--primary-color);
            color: var(--white);
            border: none;
            padding: var(--space-3) var(--space-6);
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            cursor: pointer;
            margin-top: var(--space-4);
            transition: background 0.3s ease;
        }

        .retry-button:hover {
            background: var(--primary-dark);
        }

        /* Support Section */
        .support-section {
            text-align: center;
            margin-top: var(--space-12);
            padding: var(--space-6);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .support-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--space-2);
        }

        .support-text {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: var(--space-4);
        }

        .support-email {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .support-email:hover {
            text-decoration: underline;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-4);
            }

            .main-content {
                padding: var(--space-12) 0;
            }

            .page-title {
                font-size: var(--font-size-3xl);
            }

            .page-subtitle {
                font-size: var(--font-size-lg);
            }

            .payment-container {
                padding: var(--space-4);
                margin: 0 var(--space-2);
            }

            .trust-indicators {
                gap: var(--space-4);
            }

            .header-content {
                flex-direction: column;
                gap: var(--space-4);
                text-align: center;
            }

            .step-indicator {
                gap: var(--space-2);
                margin-bottom: var(--space-8);
            }

            .step-connector {
                width: 40px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: var(--font-size-sm);
            }

            .step-label {
                font-size: var(--font-size-xs);
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: var(--font-size-2xl);
            }

            .payment-container {
                margin: 0;
                padding: var(--space-3);
            }

            .step-indicator {
                flex-direction: column;
                gap: var(--space-3);
            }

            .step-connector {
                width: 2px;
                height: 30px;
            }

            .manual-button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="../public/closelogo.png" alt="CloseIQ Logo">
                </div>
                <a href="../index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to CloseIQ
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active">
                    <div class="step-number">1</div>
                    <div class="step-label">Free Trial Signup</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-label">Account Setup</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-label">Welcome</div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Step 1 of 3: Secure Your Free Trial</h1>
                <p class="page-subtitle">$97/month after trial • Cancel anytime</p>
                
                <div class="trust-indicators">
                    <div class="trust-indicator">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure Payment</span>
                    </div>
                    <div class="trust-indicator">
                        <i class="fas fa-clock"></i>
                        <span>7-Day Free Trial</span>
                    </div>
                    <div class="trust-indicator">
                        <i class="fas fa-times-circle"></i>
                        <span>Cancel Anytime</span>
                    </div>
                </div>
            </div>

            <!-- Payment Container -->
            <div class="payment-container">
                <!-- Auto Popup Status -->
                <div class="auto-popup-status" id="popupStatus">
                    <div class="popup-icon">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                    <h3 class="popup-title">Redirecting to secure trial signup - you won't be charged for 7 days...</h3>
                    <p class="popup-description">
                        Your secure Stripe trial signup window is opening automatically. Complete your trial signup there and you'll be redirected to the next step.
                    </p>
                    <button class="manual-button" onclick="openPaymentPopup()">
                        Start Your Free Trial
                    </button>
                    <p class="fallback-text">
                        Click above if the payment window doesn't open automatically
                    </p>
                </div>
            </div>

            <!-- Support Section -->
            <div class="support-section">
                <h3 class="support-title">Need Help?</h3>
                <p class="support-text">
                    Having trouble with your payment? Our team is here to help.
                </p>
                <a href="mailto:<EMAIL>" class="support-email">
                    <EMAIL>
                </a>
            </div>
        </div>
    </main>

    <script>
        // Payment popup management
        let paymentPopup = null;
        let popupCheckInterval = null;
        const STRIPE_PAYMENT_URL = 'https://buy.stripe.com/5kQ4gzg0AeWfanseKx1VK05';

        function openPaymentPopup() {
            // Close any existing popup
            if (paymentPopup && !paymentPopup.closed) {
                paymentPopup.close();
            }

            // Calculate popup position (centered)
            const width = 600;
            const height = 700;
            const left = (screen.width - width) / 2;
            const top = (screen.height - height) / 2;

            // Open payment popup
            paymentPopup = window.open(
                STRIPE_PAYMENT_URL,
                'stripe-payment',
                `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
            );

            // Handle popup blocked
            if (!paymentPopup || paymentPopup.closed || typeof paymentPopup.closed === 'undefined') {
                handlePopupBlocked();
                return;
            }

            // Monitor popup closure
            monitorPopupClosure();
            updateUIForOpenPopup();
        }

        function monitorPopupClosure() {
            popupCheckInterval = setInterval(() => {
                if (paymentPopup && paymentPopup.closed) {
                    clearInterval(popupCheckInterval);
                    handlePopupClosed();
                }
            }, 1000);
        }

        function handlePopupClosed() {
            console.log('Payment popup closed');
            
            // Show message encouraging payment completion (Stripe will handle redirect if payment was successful)
            const popupStatus = document.getElementById('popupStatus');
            popupStatus.innerHTML = `
                <div class="popup-icon" style="color: #f59e0b;">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="popup-title">Complete Your Payment</h3>
                <p class="popup-description">
                    If you completed your payment, you'll be automatically redirected to account setup. If not, please click below to complete your 7-day free trial signup.
                </p>
                <button class="manual-button" onclick="openPaymentPopup()">
                    Complete Payment
                </button>
                <p class="fallback-text">
                    Need help? Contact <NAME_EMAIL>
                </p>
            `;
        }

        function handlePopupBlocked() {
            console.log('Popup blocked, showing manual option');
            const popupStatus = document.getElementById('popupStatus');
            popupStatus.innerHTML = `
                <div class="popup-icon" style="color: #f59e0b;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="popup-title">Popup Blocked</h3>
                <p class="popup-description">
                    Your browser blocked the payment window. Please click the button below to open the secure payment page.
                </p>
                <button class="manual-button" onclick="window.open('${STRIPE_PAYMENT_URL}', '_blank')">
                    Open Payment Page
                </button>
                <p class="fallback-text">
                    You'll be redirected back after completing payment
                </p>
            `;
        }

        function updateUIForOpenPopup() {
            const popupStatus = document.getElementById('popupStatus');
            popupStatus.innerHTML = `
                <div class="popup-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="popup-title">Complete Your Payment</h3>
                <p class="popup-description">
                    Please complete your payment in the popup window. After payment, you'll automatically continue to account setup.
                </p>
                <button class="manual-button" onclick="openPaymentPopup()">
                    Reopen Payment Window
                </button>
                <p class="fallback-text">
                    Click above if the payment window closed accidentally
                </p>
            `;
        }

        // Auto-open popup when page loads
        function autoOpenPayment() {
            console.log('Auto-opening payment popup...');
            setTimeout(() => {
                openPaymentPopup();
            }, 1500); // 1.5 second delay for better UX
        }

        // Page initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CloseIQ Signup page loaded');
            
            // Auto-open payment popup
            autoOpenPayment();
        });

        // Handle page visibility (if user comes back to this tab)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && paymentPopup && paymentPopup.closed) {
                // User came back and popup is closed, likely completed payment
                if (popupCheckInterval) {
                    clearInterval(popupCheckInterval);
                    handlePopupClosed();
                }
            }
        });

        // Clean up on page unload
        window.addEventListener('beforeunload', function() {
            if (popupCheckInterval) {
                clearInterval(popupCheckInterval);
            }
            if (paymentPopup && !paymentPopup.closed) {
                paymentPopup.close();
            }
        });
    </script>
</body>
</html>
