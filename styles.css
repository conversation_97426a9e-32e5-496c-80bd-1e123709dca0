/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Bold & Vibrant Color Palette */
    --primary-color: #00a8ff;
    --primary-dark: #0084c7;
    --secondary-color: #e100ff;
    --accent-color: #ef4444;
    --success-color: #10b981;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --bg-dark: #050914;
    --bg-darker: #030712;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #00a8ff 0%, #e100ff 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Typography */
    --font-family: 'Inter', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--bg-dark) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Collapsed state using scroll-driven animations */
.header.scrolled {
    padding: var(--space-2) 0;
    background: rgba(5, 9, 20, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled .header-content {
    padding: var(--space-2) 0;
}

/* Logo scaling on scroll */
.logo {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled .logo img {
    height: 32px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Nav menu adjustments for collapsed state */
.header.scrolled .nav-menu a {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-sm);
}

.header.scrolled .cta-button {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    gap: var(--space-8);
    align-items: center;
}

.nav-menu a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-family: var(--font-family);
    font-weight: 600;
    font-size: var(--font-size-base);
    letter-spacing: 0.025em;
    transition: all 0.3s ease;
    position: relative;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--border-radius-md);
}

.nav-menu a:hover {
    color: var(--white);
    text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.logo img {
    height: 40px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
}

.mobile-menu-toggle {
    display: none;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    cursor: pointer;
}

/* CTA Button Styles */
.cta-button {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.cta-button.primary {
    background: var(--gradient-primary);
    color: var(--white);
    font-weight: 700;
    font-size: var(--font-size-lg);
    padding: var(--space-4) var(--space-8);
}

.cta-button.large {
    font-size: var(--font-size-xl);
    padding: var(--space-5) var(--space-10);
}

.cta-button.secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    font-weight: 600;
}

.cta-button.secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Button Group Styling */
.button-group {
    display: flex;
    gap: var(--space-4);
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.button-group .cta-button {
    flex: 0 0 auto;
    min-width: 160px;
    /* Ensure consistent sizing for buttons in a group */
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Override the primary button larger padding when in a button group */
.button-group .cta-button.primary {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    height: 48px;
}

/* Mobile-only elements - hidden by default */
.hero-cta-mobile,
.mobile-section-cta {
    display: none;
}

/* Hero Section */
.hero {
    padding: calc(80px + var(--space-20)) 0 var(--space-20);
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    color: var(--white);
    overflow: hidden;
    position: relative;
}

/* Space Dust Particles */
.hero::before,
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.hero::before {
    background-image: 
        radial-gradient(5px 5px at 20% 30%, #00a8ff, transparent),
        radial-gradient(3px 3px at 40% 70%, #e100ff, transparent),
        radial-gradient(2px 2px at 90% 40%, #f093fb, transparent),
        radial-gradient(4px 4px at 60% 10%, #00a8ff, transparent),
        radial-gradient(6px 6px at 80% 80%, #f5576c, transparent),
        radial-gradient(2px 2px at 10% 90%, #e100ff, transparent),
        radial-gradient(3px 3px at 70% 60%, #f093fb, transparent),
        radial-gradient(4px 4px at 30% 20%, #f5576c, transparent),
        radial-gradient(2px 2px at 50% 50%, #00a8ff, transparent),
        radial-gradient(3px 3px at 85% 15%, #e100ff, transparent),
        radial-gradient(2px 2px at 75% 45%, #ffffff, transparent),
        radial-gradient(1px 1px at 35% 25%, #00a8ff, transparent),
        radial-gradient(2px 2px at 65% 75%, #f093fb, transparent),
        radial-gradient(1px 1px at 15% 60%, #00a8ff, transparent),
        radial-gradient(5px 5px at 95% 20%, #e100ff, transparent),
        radial-gradient(2px 2px at 25% 85%, #f093fb, transparent),
        radial-gradient(3px 3px at 55% 30%, #f5576c, transparent),
        radial-gradient(1px 1px at 80% 65%, #ffffff, transparent),
        radial-gradient(4px 4px at 5% 45%, #00a8ff, transparent),
        radial-gradient(2px 2px at 45% 5%, #e100ff, transparent),
        radial-gradient(3px 3px at 90% 75%, #f093fb, transparent),
        radial-gradient(1px 1px at 70% 85%, #f5576c, transparent),
        radial-gradient(6px 6px at 15% 15%, #00a8ff, transparent),
        radial-gradient(2px 2px at 85% 35%, #e100ff, transparent),
        radial-gradient(1px 1px at 60% 90%, #ffffff, transparent),
        radial-gradient(3px 3px at 95% 60%, #f093fb, transparent),
        radial-gradient(4px 4px at 25% 40%, #f5576c, transparent),
        radial-gradient(1px 1px at 5% 25%, #00a8ff, transparent),
        radial-gradient(2px 2px at 75% 15%, #e100ff, transparent),
        radial-gradient(5px 5px at 45% 75%, #f093fb, transparent);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: float 15s ease-in-out infinite;
    filter: drop-shadow(0 0 3px rgba(0, 168, 255, 0.5)) drop-shadow(0 0 6px rgba(225, 0, 255, 0.3));
}

.hero::after {
    background-image:
        radial-gradient(3px 3px at 25% 80%, #f093fb, transparent),
        radial-gradient(4px 4px at 75% 25%, #e100ff, transparent),
        radial-gradient(2px 2px at 45% 35%, #00a8ff, transparent),
        radial-gradient(3px 3px at 65% 85%, #f5576c, transparent),
        radial-gradient(4px 4px at 15% 45%, #f093fb, transparent),
        radial-gradient(2px 2px at 95% 70%, #00a8ff, transparent),
        radial-gradient(3px 3px at 35% 95%, #e100ff, transparent),
        radial-gradient(4px 4px at 55% 5%, #f5576c, transparent),
        radial-gradient(2px 2px at 5% 25%, #f093fb, transparent),
        radial-gradient(3px 3px at 85% 55%, #e100ff, transparent),
        radial-gradient(2px 2px at 45% 15%, #ffffff, transparent),
        radial-gradient(1px 1px at 25% 45%, #00a8ff, transparent),
        radial-gradient(3px 3px at 75% 65%, #f5576c, transparent),
        radial-gradient(1px 1px at 90% 30%, #f093fb, transparent),
        radial-gradient(5px 5px at 10% 70%, #e100ff, transparent),
        radial-gradient(2px 2px at 30% 10%, #00a8ff, transparent),
        radial-gradient(4px 4px at 85% 90%, #f5576c, transparent),
        radial-gradient(1px 1px at 60% 55%, #ffffff, transparent),
        radial-gradient(3px 3px at 20% 65%, #f093fb, transparent),
        radial-gradient(6px 6px at 90% 10%, #e100ff, transparent),
        radial-gradient(2px 2px at 40% 80%, #00a8ff, transparent),
        radial-gradient(1px 1px at 70% 20%, #f5576c, transparent),
        radial-gradient(4px 4px at 95% 45%, #f093fb, transparent),
        radial-gradient(2px 2px at 15% 35%, #e100ff, transparent),
        radial-gradient(3px 3px at 50% 70%, #00a8ff, transparent),
        radial-gradient(1px 1px at 80% 50%, #ffffff, transparent),
        radial-gradient(5px 5px at 35% 25%, #f5576c, transparent),
        radial-gradient(2px 2px at 65% 95%, #f093fb, transparent),
        radial-gradient(1px 1px at 10% 15%, #e100ff, transparent),
        radial-gradient(3px 3px at 55% 40%, #00a8ff, transparent);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: float 18s ease-in-out infinite reverse;
    filter: drop-shadow(0 0 4px rgba(240, 147, 251, 0.5)) drop-shadow(0 0 8px rgba(245, 87, 108, 0.3));
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 1;
    }
    25% {
        transform: translateY(-20px) translateX(10px);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) translateX(-5px);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-15px) translateX(8px);
        opacity: 0.7;
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.9;
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: flex-start;
}

.trust-indicator {
    font-size: var(--font-size-sm);
    opacity: 1;
    font-weight: 500;
}

/* Make trust indicator white only in features section for better readability */
.features .trust-indicator {
    color: var(--white);
}

/* Integration Scrolling Banner */
.integration-banner {
    background: #070b15;
    padding: var(--space-3) 0;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.integration-banner .container {
    max-width: 100%;
    padding: 0;
}

.scrolling-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
}

.logo-container {
    overflow: hidden;
    width: 100%;
    position: relative;
}

.scrolling-logos {
    display: flex;
    align-items: center;
    gap: 60px;
    animation: scroll 118.125s linear infinite;
    width: max-content;
    padding: 0;
}

.scrolling-logos img {
    height: 35px;
    width: auto;
    opacity: 0.85;
    filter: brightness(2.5);
    transition: all 0.3s ease;
    object-fit: contain;
    flex-shrink: 0;
}

.scrolling-logos img:hover {
    opacity: 1;
    filter: brightness(3);
    transform: scale(1.15);
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .scrolling-logos img {
        height: 40px;
    }
    
    .scrolling-logos {
        gap: var(--space-8);
        animation-duration: 118.125s;
    }
    
    .integration-banner {
        padding: var(--space-6) 0;
    }
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.video-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    aspect-ratio: 16/9;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    padding: 4px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(0, 168, 255, 0.3),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
    filter: drop-shadow(0 0 20px rgba(225, 0, 255, 0.2));
}

.video-container::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
}

.video-container iframe {
    position: relative;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: calc(var(--border-radius-xl) - 4px);
    background: var(--bg-darker);
    z-index: 1;
    box-sizing: border-box;
    overflow: hidden;
}

.screenshot-placeholder {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-xl);
    padding: var(--space-16);
    text-align: center;
    width: 100%;
    max-width: 500px;
    aspect-ratio: 16/10;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-4);
    overflow: hidden;
}

.screenshot-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    max-width: 100%;
    max-height: 100%;
}

.screenshot-placeholder i {
    font-size: 4rem;
    opacity: 0.7;
}

.screenshot-placeholder p {
    font-size: var(--font-size-lg);
    font-weight: 600;
    opacity: 0.8;
}

/* Section Titles */
.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    text-align: center;
    margin-bottom: var(--space-16);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    font-weight: 500;
    text-align: center;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto var(--space-12);
    padding: 0 var(--space-4);
}

/* Benefits Section */
.benefits {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
}

.benefit-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.benefit-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--text-dark);
}

.benefit-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Features Section */
.features {
    padding: var(--space-20) 0;
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    position: relative;
    overflow: hidden;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: var(--space-6);
    position: relative;
    z-index: 2;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: var(--space-6);
    border-radius: var(--border-radius-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.feature-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 168, 255, 0.3);
}

.feature-card i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--space-4);
    filter: drop-shadow(0 0 8px rgba(0, 168, 255, 0.5));
}

.feature-card h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--white);
}

.feature-card p {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* Coming Soon Badge for Features */
.coming-soon-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--gradient-secondary);
    color: var(--white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
    z-index: 3;
}

.feature-card.coming-soon {
    opacity: 0.85;
    border-color: rgba(240, 147, 251, 0.3);
}

.feature-card.coming-soon:hover {
    border-color: rgba(240, 147, 251, 0.5);
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.2);
}

/* Screenshots Section */
.screenshots {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.screenshots-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-16);
}

.screenshot-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    align-items: flex-start;
    background: var(--gradient-primary);
    padding: var(--space-10);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    min-height: 400px;
}

.screenshot-item:nth-child(even) {
    direction: rtl;
}

.screenshot-item:nth-child(even) > * {
    direction: ltr;
}

.screenshot-item .screenshot-placeholder {
    background: var(--gray-100);
    border: 2px dashed var(--gray-300);
    color: var(--text-light);
    aspect-ratio: 16/10;
    overflow: hidden;
    padding: var(--space-4);
    position: relative;
    min-height: 350px;
}

.screenshot-item .screenshot-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    max-width: 100%;
    max-height: 100%;
}

/* Image Loading States */
.image-loader {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.image-loader img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.5s ease;
    position: absolute;
    top: 0;
    left: 0;
}

.image-loader.loaded img {
    opacity: 1;
}

.image-loading,
.image-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--text-light);
    transition: opacity 0.3s ease;
}

.image-loader.loaded .image-loading,
.image-loader.error .image-loading {
    opacity: 0;
    pointer-events: none;
}

.image-error {
    opacity: 0;
    pointer-events: none;
}

.image-loader.error .image-error {
    opacity: 1;
    pointer-events: auto;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-300);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.image-loading span,
.image-error span {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.image-error i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-2);
    opacity: 0.5;
}

/* Progressive image loading with blur effect */
.image-loader::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        var(--gray-100) 0%,
        var(--gray-200) 25%,
        var(--gray-100) 50%,
        var(--gray-200) 75%,
        var(--gray-100) 100%
    );
    background-size: 200% 200%;
    animation: shimmer 2s ease-in-out infinite;
    opacity: 1;
    transition: opacity 0.5s ease;
    z-index: 1;
}

.image-loader.loaded::before {
    opacity: 0;
}

@keyframes shimmer {
    0% {
        background-position: 200% 200%;
    }
    100% {
        background-position: -200% -200%;
    }
}

.screenshot-content h4 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--white);
}

.screenshot-content p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

/* Screenshot Features List */
.screenshot-features {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.screenshot-features li {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    font-weight: 500;
}

.screenshot-features li i {
    color: var(--success-color);
    font-size: var(--font-size-xs);
    margin-top: 2px;
    filter: drop-shadow(0 0 2px rgba(16, 185, 129, 0.3));
    flex-shrink: 0;
}

/* Pricing Section */
.pricing {
    padding: var(--space-20) 0;
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    position: relative;
    overflow: hidden;
}

.section-title.centered {
    text-align: center;
}

/* Single Pricing Card Layout */
.single-pricing-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}

.single-pricing-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: var(--space-10);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    width: 100%;
    max-width: 920px;
    text-align: center;
}

.single-pricing-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
    border-color: rgba(0, 168, 255, 0.5);
}

/* Pre-launch Badge */
.prelaunch-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-secondary);
    color: var(--white);
    padding: var(--space-3) var(--space-8);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-weight: 700;
    box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Pricing Subheading */
.pricing-subheading {
    margin: var(--space-8) 0 var(--space-10);
    color: rgba(255, 255, 255, 0.9);
}

.pricing-subheading p {
    margin-bottom: var(--space-2);
    font-size: var(--font-size-base);
    line-height: 1.5;
}

.pricing-subheading p:last-child {
    margin-bottom: 0;
}

/* Pricing Display */
.pricing-display {
    margin: var(--space-8) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
}

.old-price {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.5);
    text-decoration: line-through;
    font-weight: 600;
}

.new-price-container {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--space-1);
}

.new-price-container .currency {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    font-weight: 700;
}

.new-price {
    font-size: 4rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
    filter: drop-shadow(0 0 10px rgba(0, 168, 255, 0.5));
}

.new-price-container .period {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
}

/* Features Section */
.features-section {
    margin: var(--space-10) 0;
    text-align: left;
}

.features-header {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--space-6);
    text-align: center;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.single-pricing-card .features-list {
    list-style: none;
    margin-bottom: var(--space-8);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-3) var(--space-2);
}

.single-pricing-card .features-list li {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--white);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.single-pricing-card .features-list i {
    color: var(--success-color);
    font-size: var(--font-size-xs);
    filter: drop-shadow(0 0 4px rgba(16, 185, 129, 0.5));
    flex-shrink: 0;
}

/* Coming Soon Badge for Pricing */
.pricing-coming-soon {
    position: relative;
    opacity: 0.8;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.pricing-coming-soon .coming-soon-badge {
    position: static;
    display: inline-block;
    background: var(--gradient-secondary);
    color: var(--white);
    padding: 1px var(--space-2);
    border-radius: var(--border-radius-md);
    font-size: 9px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 1px 4px rgba(240, 147, 251, 0.3);
    margin-left: var(--space-1);
    vertical-align: middle;
    flex-shrink: 0;
}

/* CTA Button */
.single-card-cta {
    width: 100%;
    margin: var(--space-8) 0 var(--space-6);
    font-size: var(--font-size-xl);
    padding: var(--space-5) var(--space-8);
    font-weight: 700;
}

/* Guarantee Text */
.guarantee-text {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
    text-align: center;
    margin: 0;
}

/* Final CTA Section */
.final-cta {
    padding: var(--space-20) 0;
    background: var(--gradient-primary);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--space-6);
}

.cta-content p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.9;
}

.cta-note {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin-top: var(--space-4);
}

/* Final CTA Button White Outline */
.final-cta .cta-button.primary {
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3), 
                0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Footer */
.footer {
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    color: var(--white);
    padding: var(--space-16) 0 var(--space-8);
    position: relative;
    overflow: hidden;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
    position: relative;
    z-index: 2;
}

.footer-logo img {
    height: 32px;
    width: auto;
    max-width: 150px;
    margin-bottom: var(--space-2);
    object-fit: contain;
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: flex;
    gap: var(--space-6);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--white);
    text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--space-8);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    position: relative;
    z-index: 2;
}

/* Value Comparison Section */
.value-comparison {
    padding: var(--space-20) 0;
    background: var(--bg-dark);
    position: relative;
    overflow: hidden;
}

/* Enhanced Star/Particle Effects for Value Comparison */
.value-comparison::before,
.value-comparison::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.value-comparison::before {
    background-image: 
        radial-gradient(5px 5px at 15% 25%, #00a8ff, transparent),
        radial-gradient(3px 3px at 35% 65%, #e100ff, transparent),
        radial-gradient(2px 2px at 85% 35%, #f093fb, transparent),
        radial-gradient(4px 4px at 55% 8%, #00a8ff, transparent),
        radial-gradient(6px 6px at 75% 75%, #f5576c, transparent),
        radial-gradient(2px 2px at 8% 85%, #e100ff, transparent),
        radial-gradient(3px 3px at 65% 55%, #f093fb, transparent),
        radial-gradient(4px 4px at 25% 15%, #f5576c, transparent),
        radial-gradient(2px 2px at 45% 45%, #00a8ff, transparent),
        radial-gradient(3px 3px at 80% 12%, #e100ff, transparent),
        radial-gradient(2px 2px at 70% 40%, #ffffff, transparent),
        radial-gradient(1px 1px at 30% 20%, #00a8ff, transparent),
        radial-gradient(2px 2px at 60% 70%, #f093fb, transparent),
        radial-gradient(1px 1px at 12% 55%, #00a8ff, transparent),
        radial-gradient(5px 5px at 90% 18%, #e100ff, transparent),
        radial-gradient(2px 2px at 20% 80%, #f093fb, transparent),
        radial-gradient(3px 3px at 50% 25%, #f5576c, transparent),
        radial-gradient(1px 1px at 75% 60%, #ffffff, transparent),
        radial-gradient(4px 4px at 3% 40%, #00a8ff, transparent),
        radial-gradient(2px 2px at 40% 3%, #e100ff, transparent),
        radial-gradient(3px 3px at 85% 70%, #f093fb, transparent),
        radial-gradient(1px 1px at 65% 80%, #f5576c, transparent),
        radial-gradient(6px 6px at 12% 12%, #00a8ff, transparent),
        radial-gradient(2px 2px at 80% 30%, #e100ff, transparent),
        radial-gradient(1px 1px at 55% 85%, #ffffff, transparent),
        radial-gradient(3px 3px at 90% 55%, #f093fb, transparent),
        radial-gradient(4px 4px at 20% 35%, #f5576c, transparent),
        radial-gradient(1px 1px at 2% 20%, #00a8ff, transparent),
        radial-gradient(2px 2px at 70% 12%, #e100ff, transparent),
        radial-gradient(5px 5px at 40% 70%, #f093fb, transparent),
        radial-gradient(3px 3px at 92% 42%, #00a8ff, transparent),
        radial-gradient(2px 2px at 18% 38%, #f5576c, transparent),
        radial-gradient(4px 4px at 62% 15%, #e100ff, transparent),
        radial-gradient(1px 1px at 38% 72%, #ffffff, transparent),
        radial-gradient(3px 3px at 82% 88%, #f093fb, transparent),
        radial-gradient(2px 2px at 5% 68%, #00a8ff, transparent),
        radial-gradient(4px 4px at 47% 92%, #f5576c, transparent),
        radial-gradient(1px 1px at 77% 28%, #e100ff, transparent),
        radial-gradient(2px 2px at 28% 58%, #ffffff, transparent),
        radial-gradient(3px 3px at 87% 5%, #f093fb, transparent),
        radial-gradient(5px 5px at 15% 75%, #00a8ff, transparent),
        radial-gradient(2px 2px at 52% 32%, #e100ff, transparent),
        radial-gradient(1px 1px at 95% 78%, #f5576c, transparent),
        radial-gradient(4px 4px at 32% 88%, #ffffff, transparent),
        radial-gradient(3px 3px at 68% 22%, #f093fb, transparent),
        radial-gradient(2px 2px at 7% 52%, #00a8ff, transparent),
        radial-gradient(1px 1px at 42% 7%, #e100ff, transparent),
        radial-gradient(3px 3px at 88% 62%, #f5576c, transparent),
        radial-gradient(4px 4px at 22% 68%, #f093fb, transparent),
        radial-gradient(2px 2px at 58% 95%, #ffffff, transparent),
        radial-gradient(1px 1px at 95% 35%, #00a8ff, transparent),
        radial-gradient(5px 5px at 35% 5%, #e100ff, transparent),
        radial-gradient(3px 3px at 72% 82%, #f5576c, transparent),
        radial-gradient(2px 2px at 13% 28%, #f093fb, transparent),
        radial-gradient(1px 1px at 48% 63%, #ffffff, transparent),
        radial-gradient(4px 4px at 83% 23%, #00a8ff, transparent),
        radial-gradient(2px 2px at 23% 93%, #e100ff, transparent),
        radial-gradient(3px 3px at 63% 38%, #f5576c, transparent),
        radial-gradient(1px 1px at 8% 8%, #f093fb, transparent),
        radial-gradient(2px 2px at 53% 78%, #ffffff, transparent),
        radial-gradient(4px 4px at 93% 93%, #00a8ff, transparent),
        radial-gradient(3px 3px at 28% 48%, #e100ff, transparent),
        radial-gradient(1px 1px at 78% 3%, #f5576c, transparent);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: float 12s ease-in-out infinite;
    filter: drop-shadow(0 0 3px rgba(0, 168, 255, 0.5)) drop-shadow(0 0 6px rgba(225, 0, 255, 0.3));
}

.value-comparison::after {
    background-image:
        radial-gradient(3px 3px at 22% 75%, #f093fb, transparent),
        radial-gradient(4px 4px at 72% 22%, #e100ff, transparent),
        radial-gradient(2px 2px at 42% 32%, #00a8ff, transparent),
        radial-gradient(3px 3px at 62% 82%, #f5576c, transparent),
        radial-gradient(4px 4px at 12% 42%, #f093fb, transparent),
        radial-gradient(2px 2px at 92% 67%, #00a8ff, transparent),
        radial-gradient(3px 3px at 32% 92%, #e100ff, transparent),
        radial-gradient(4px 4px at 52% 2%, #f5576c, transparent),
        radial-gradient(2px 2px at 2% 22%, #f093fb, transparent),
        radial-gradient(3px 3px at 82% 52%, #e100ff, transparent),
        radial-gradient(2px 2px at 42% 12%, #ffffff, transparent),
        radial-gradient(1px 1px at 22% 42%, #00a8ff, transparent),
        radial-gradient(3px 3px at 72% 62%, #f5576c, transparent),
        radial-gradient(1px 1px at 87% 27%, #f093fb, transparent),
        radial-gradient(5px 5px at 7% 67%, #e100ff, transparent),
        radial-gradient(2px 2px at 27% 7%, #00a8ff, transparent),
        radial-gradient(4px 4px at 82% 87%, #f5576c, transparent),
        radial-gradient(1px 1px at 57% 52%, #ffffff, transparent),
        radial-gradient(3px 3px at 17% 62%, #f093fb, transparent),
        radial-gradient(6px 6px at 87% 7%, #e100ff, transparent),
        radial-gradient(2px 2px at 37% 77%, #00a8ff, transparent),
        radial-gradient(1px 1px at 67% 17%, #f5576c, transparent),
        radial-gradient(4px 4px at 92% 42%, #f093fb, transparent),
        radial-gradient(2px 2px at 12% 32%, #e100ff, transparent),
        radial-gradient(3px 3px at 47% 67%, #00a8ff, transparent),
        radial-gradient(1px 1px at 77% 47%, #ffffff, transparent),
        radial-gradient(5px 5px at 32% 22%, #f5576c, transparent),
        radial-gradient(2px 2px at 62% 92%, #f093fb, transparent),
        radial-gradient(1px 1px at 7% 12%, #e100ff, transparent),
        radial-gradient(3px 3px at 52% 37%, #00a8ff, transparent),
        radial-gradient(4px 4px at 97% 72%, #f5576c, transparent),
        radial-gradient(2px 2px at 17% 97%, #ffffff, transparent),
        radial-gradient(3px 3px at 67% 47%, #f093fb, transparent),
        radial-gradient(1px 1px at 37% 2%, #e100ff, transparent),
        radial-gradient(2px 2px at 87% 57%, #00a8ff, transparent),
        radial-gradient(4px 4px at 2% 82%, #f5576c, transparent),
        radial-gradient(3px 3px at 57% 27%, #ffffff, transparent),
        radial-gradient(1px 1px at 27% 77%, #f093fb, transparent),
        radial-gradient(5px 5px at 77% 2%, #e100ff, transparent),
        radial-gradient(2px 2px at 47% 87%, #00a8ff, transparent),
        radial-gradient(3px 3px at 92% 27%, #f5576c, transparent),
        radial-gradient(1px 1px at 22% 52%, #ffffff, transparent),
        radial-gradient(4px 4px at 72% 97%, #f093fb, transparent),
        radial-gradient(2px 2px at 42% 22%, #e100ff, transparent),
        radial-gradient(3px 3px at 97% 62%, #00a8ff, transparent),
        radial-gradient(1px 1px at 12% 87%, #f5576c, transparent),
        radial-gradient(2px 2px at 62% 12%, #ffffff, transparent),
        radial-gradient(4px 4px at 32% 62%, #f093fb, transparent),
        radial-gradient(3px 3px at 87% 37%, #e100ff, transparent),
        radial-gradient(1px 1px at 2% 57%, #00a8ff, transparent),
        radial-gradient(5px 5px at 52% 92%, #f5576c, transparent),
        radial-gradient(2px 2px at 82% 17%, #ffffff, transparent),
        radial-gradient(3px 3px at 17% 47%, #f093fb, transparent),
        radial-gradient(4px 4px at 67% 72%, #e100ff, transparent),
        radial-gradient(1px 1px at 97% 97%, #00a8ff, transparent),
        radial-gradient(2px 2px at 27% 37%, #f5576c, transparent),
        radial-gradient(3px 3px at 77% 87%, #ffffff, transparent),
        radial-gradient(1px 1px at 47% 17%, #f093fb, transparent),
        radial-gradient(4px 4px at 7% 77%, #e100ff, transparent),
        radial-gradient(2px 2px at 57% 57%, #00a8ff, transparent),
        radial-gradient(3px 3px at 37% 82%, #f5576c, transparent),
        radial-gradient(1px 1px at 92% 12%, #ffffff, transparent);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: float 15s ease-in-out infinite reverse;
    filter: drop-shadow(0 0 4px rgba(240, 147, 251, 0.5)) drop-shadow(0 0 8px rgba(245, 87, 108, 0.3));
}

.comparison-table-wrapper {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-xl);
    padding: var(--space-8);
    margin-bottom: var(--space-12);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow-x: auto;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family);
    background: transparent;
}

.comparison-table th {
    background: rgba(0, 168, 255, 0.2);
    color: var(--white);
    font-weight: 700;
    font-size: var(--font-size-lg);
    padding: var(--space-5) var(--space-4);
    text-align: left;
    border-bottom: 2px solid rgba(0, 168, 255, 0.3);
    position: sticky;
    top: 0;
    z-index: 1;
}

.comparison-table th.center-header {
    text-align: center;
}

.comparison-table th.logo-header {
    font-style: italic;
    opacity: 0.8;
}

.comparison-table td {
    padding: var(--space-4) var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: var(--font-size-base);
    font-weight: 500;
    vertical-align: middle;
}

.comparison-table tr {
    transition: background-color 0.3s ease;
}

.comparison-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.comparison-table .cost-item {
    color: #ff6b6b;
    font-weight: 600;
    background: rgba(255, 107, 107, 0.1);
    border-radius: var(--border-radius-md);
    padding: var(--space-2) var(--space-3);
    text-align: center;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.comparison-table .included-item {
    color: var(--success-color);
    font-weight: 600;
    background: rgba(16, 185, 129, 0.1);
    border-radius: var(--border-radius-md);
    padding: var(--space-2) var(--space-3);
    text-align: center;
    border: 1px solid rgba(16, 185, 129, 0.2);
    filter: drop-shadow(0 0 4px rgba(16, 185, 129, 0.3));
}

.comparison-table .total-row {
    background: rgba(0, 168, 255, 0.1);
    border-top: 2px solid rgba(0, 168, 255, 0.3);
}

.comparison-table .total-row td {
    padding: var(--space-5) var(--space-4);
    font-size: var(--font-size-lg);
    border-bottom: none;
}

.comparison-table .total-cost {
    color: #ff6b6b;
    font-weight: 700;
    font-size: var(--font-size-xl);
    background: rgba(255, 107, 107, 0.15);
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(255, 107, 107, 0.3);
    text-align: center;
}

.comparison-table .total-included {
    color: var(--primary-color);
    font-weight: 700;
    font-size: var(--font-size-xl);
    background: rgba(0, 168, 255, 0.15);
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(0, 168, 255, 0.3);
    text-align: center;
    filter: drop-shadow(0 0 8px rgba(0, 168, 255, 0.4));
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-3) var(--space-4);
}

.comparison-table .crossed-out {
    text-decoration: line-through;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
    font-size: var(--font-size-base);
    opacity: 0.7;
}

.comparison-table .actual-price {
    color: var(--primary-color);
    font-weight: 800;
    font-size: var(--font-size-xl);
    filter: drop-shadow(0 0 8px rgba(0, 168, 255, 0.6));
    text-shadow: 0 0 10px rgba(0, 168, 255, 0.3);
}

/* Savings Highlight */
.savings-highlight {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: var(--space-8);
}

.savings-badge {
    background: var(--gradient-secondary);
    color: var(--white);
    padding: var(--space-6) var(--space-12);
    border-radius: var(--border-radius-xl);
    box-shadow: 
        0 20px 40px rgba(245, 87, 108, 0.4),
        0 0 30px rgba(240, 147, 251, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-align: center;
    transform: scale(1.05);
    animation: pulse-glow 3s ease-in-out infinite;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.savings-text {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-1);
    opacity: 0.9;
}

.savings-amount {
    display: block;
    font-size: var(--font-size-4xl);
    font-weight: 800;
    line-height: 1;
    margin-bottom: var(--space-1);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.savings-period {
    display: block;
    font-size: var(--font-size-base);
    font-weight: 500;
    opacity: 0.8;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 
            0 20px 40px rgba(245, 87, 108, 0.4),
            0 0 30px rgba(240, 147, 251, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }
    50% {
        box-shadow: 
            0 25px 50px rgba(245, 87, 108, 0.6),
            0 0 40px rgba(240, 147, 251, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transform: scale(1.08);
    }
}

/* Animation Classes */
.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-up {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.slide-in-left.animate,
.slide-in-right.animate,
.slide-in-up.animate {
    opacity: 1;
    transform: translate(0);
}

/* FAQ Section */
.faq {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.faq-grid {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.faq-item {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.faq-question:hover {
    background: var(--gray-50);
}

.faq-question h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.4;
    flex: 1;
    padding-right: var(--space-4);
}

.faq-icon {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background: var(--gray-50);
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding: 0 var(--space-6) var(--space-6);
}

.faq-answer p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    padding-top: var(--space-2);
}

/* Mobile Logo Styles */
.mobile-logo {
    display: none;
}

/* Subtitle Display Control */
.mobile-subtitle {
    display: none;
}

.desktop-subtitle {
    display: block;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-4);
    }
    
    /* Hide Header on Mobile */
    .header {
        display: none;
    }
    
    /* Mobile Logo */
    .mobile-logo {
        display: block;
        text-align: center;
        margin-bottom: var(--space-8);
    }
    
    .mobile-logo img {
        height: 60px;
        width: auto;
        max-width: 250px;
        object-fit: contain;
    }
    
    /* Subtitle Mobile Control */
    .desktop-subtitle {
        display: none;
    }
    
    .mobile-subtitle {
        display: block;
    }
    
    /* Hero Section Mobile */
    .hero {
        padding: var(--space-4) 0 var(--space-16);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-10);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
        margin-bottom: var(--space-4);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
        margin-bottom: var(--space-6);
        line-height: 1.5;
    }
    
    .hero-cta {
        align-items: center;
        gap: var(--space-3);
        display: none; /* Hide desktop hero CTA on mobile */
    }

    .trust-indicator {
        text-align: center;
        font-size: var(--font-size-sm);
    }

    /* Show mobile-only elements */
    .hero-cta-mobile,
    .mobile-section-cta {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
        align-items: center;
        margin-top: var(--space-6);
    }

    .hero-cta-mobile .cta-button,
    .mobile-section-cta .cta-button {
        width: 100%;
        max-width: 280px;
        text-align: center;
    }
    
    /* Video Container Mobile */
    .video-container {
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
        background: transparent;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 
                    0 20px 60px rgba(0, 0, 0, 0.2);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
    }
    
    .video-container::before {
        display: none;
    }
    
    .video-container iframe {
        border-radius: var(--border-radius-lg);
        box-sizing: border-box;
        overflow: hidden;
        max-width: 100%;
        max-height: 100%;
    }
    
    /* Section Titles */
    .section-title {
        font-size: var(--font-size-3xl);
        margin-bottom: var(--space-12);
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: var(--space-8);
        padding: 0 var(--space-2);
    }

    /* Benefits Section Mobile */
    .benefits {
        padding: var(--space-16) 0;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: var(--space-5);
    }
    
    .benefit-card {
        padding: var(--space-6);
        margin: 0 var(--space-2);
    }
    
    .benefit-card h3 {
        font-size: var(--font-size-lg);
    }
    
    .benefit-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
        margin-bottom: var(--space-4);
    }
    
    /* Features Section Mobile */
    .features {
        padding: var(--space-16) 0;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }

    /* Center the last feature card when it's alone in the row */
    .feature-card:nth-child(9) {
        grid-column: 1 / -1;
        max-width: 300px;
        margin: 0 auto;
    }

    .feature-card {
        padding: var(--space-5);
        text-align: center;
    }

    .feature-card i {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--space-3);
    }

    .feature-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--space-2);
    }
    
    /* Screenshots Section Mobile */
    .screenshots {
        padding: var(--space-16) 0;
    }
    
    .screenshot-item {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        text-align: center;
        padding: var(--space-4);
        margin: 0 var(--space-2);
    }

    .screenshot-item:nth-child(even) {
        direction: ltr;
    }

    .screenshot-item .screenshot-placeholder {
        padding: 0;
        aspect-ratio: 16/9;
        max-width: 100%;
        margin: 0 auto;
        height: auto;
        min-height: auto;
        overflow: hidden;
        border-radius: var(--border-radius-lg);
    }

    .screenshot-item .screenshot-placeholder img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: var(--border-radius-lg);
        display: block;
    }
    
    .screenshot-content h4 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--space-3);
    }
    
    /* Mobile image optimization */
    .image-loader {
        min-height: auto;
        height: 100%;
    }
    
    .loading-spinner {
        width: 30px;
        height: 30px;
        border-width: 2px;
    }
    
    .image-loading span,
    .image-error span {
        font-size: var(--font-size-xs);
    }
    
    /* Reduce shimmer animation intensity on mobile */
    .image-loader::before {
        animation-duration: 1.5s;
    }
    
    /* Value Comparison Section Mobile */
    .value-comparison {
        padding: var(--space-16) 0;
    }
    
    .comparison-table-wrapper {
        padding: var(--space-6);
        margin: 0 var(--space-2) var(--space-10);
        overflow-x: auto;
    }
    
    .comparison-table {
        min-width: 600px;
        font-size: var(--font-size-sm);
    }
    
    .comparison-table th {
        font-size: var(--font-size-base);
        padding: var(--space-4) var(--space-3);
    }
    
    .comparison-table td {
        padding: var(--space-3) var(--space-3);
        font-size: var(--font-size-sm);
    }
    
    .comparison-table .total-row td {
        padding: var(--space-4) var(--space-3);
        font-size: var(--font-size-base);
    }
    
    .comparison-table .total-cost,
    .comparison-table .total-included {
        font-size: var(--font-size-lg);
    }
    
    .savings-badge {
        padding: var(--space-5) var(--space-8);
        margin: 0 var(--space-2);
        transform: scale(1);
    }
    
    .savings-amount {
        font-size: var(--font-size-3xl);
    }
    
    .savings-text,
    .savings-period {
        font-size: var(--font-size-base);
    }

    /* Single Pricing Card Mobile */
    .pricing {
        padding: var(--space-16) 0;
    }
    
    .single-pricing-card {
        max-width: 100%;
        margin: 0 var(--space-2);
        padding: var(--space-8);
        transform: none !important;
    }
    
    .single-pricing-card:hover {
        transform: none !important;
    }
    
    .prelaunch-badge {
        top: -12px;
        padding: var(--space-2) var(--space-6);
        font-size: var(--font-size-sm);
    }
    
    .pricing-subheading {
        margin: var(--space-6) 0 var(--space-8);
    }
    
    .pricing-subheading p {
        font-size: var(--font-size-sm);
    }
    
    .new-price {
        font-size: 3rem;
    }
    
    .new-price-container .currency {
        font-size: var(--font-size-xl);
    }
    
    .new-price-container .period {
        font-size: var(--font-size-base);
    }
    
    .features-header {
        font-size: var(--font-size-lg);
        margin-bottom: var(--space-5);
    }
    
    .single-pricing-card .features-list {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4) var(--space-3);
    }
    
    .single-pricing-card .features-list li {
        font-size: var(--font-size-sm);
        margin-bottom: var(--space-3);
    }
    
    .single-card-cta {
        font-size: var(--font-size-lg);
        padding: var(--space-4) var(--space-6);
        margin: var(--space-6) 0 var(--space-5);
    }
    
    /* FAQ Section Mobile */
    .faq {
        padding: var(--space-16) 0;
    }
    
    .faq-grid {
        margin: 0 var(--space-2);
    }
    
    .faq-question {
        padding: var(--space-5);
    }
    
    .faq-question h4 {
        font-size: var(--font-size-base);
        padding-right: var(--space-3);
    }
    
    /* Footer Mobile */
    .footer {
        padding: var(--space-12) 0 var(--space-6);
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--space-5);
        text-align: center;
    }
    
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--space-4);
    }
    
    /* Button Group Mobile */
    .button-group {
        flex-direction: column;
        gap: var(--space-3);
        width: 100%;
    }

    .button-group .cta-button {
        width: 100%;
        max-width: 280px;
        min-width: auto;
    }

    /* Button Adjustments */
    .cta-button {
        padding: var(--space-3) var(--space-6);
        font-size: var(--font-size-base);
        min-height: 44px;
        border-radius: var(--border-radius-lg);
    }
    
    .cta-button.primary {
        padding: var(--space-4) var(--space-8);
        font-size: var(--font-size-lg);
        min-height: 48px;
    }
    
    .cta-button.large {
        font-size: var(--font-size-xl);
        padding: var(--space-5) var(--space-10);
        min-height: 52px;
    }
    
    /* Remove hover effects on touch devices */
    .benefit-card:hover,
    .feature-card:hover,
    .pricing-card:hover,
    .screenshot-item:hover,
    .faq-item:hover {
        transform: none;
    }

    /* Hero Integrations Mobile */
    .hero-integrations {
        margin-top: var(--space-8);
    }

    .integrations-logos {
        gap: var(--space-4);
    }

    .integration-logo {
        height: 72px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-3);
    }
    
    /* Hero Section Small Mobile */
    .hero {
        padding: calc(80px + var(--space-12)) 0 var(--space-12);
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.1;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    /* Section adjustments */
    .benefits, .features, .screenshots, .pricing, .faq {
        padding: var(--space-12) 0;
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--space-8);
    }

    .section-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: var(--space-6);
        padding: 0 var(--space-1);
    }

    /* Card adjustments */
    .benefit-card, .pricing-card, .screenshot-item {
        margin: 0 var(--space-1);
        padding: var(--space-5);
    }
    
    .benefit-card h3, .screenshot-content h4 {
        font-size: var(--font-size-base);
    }
    
    .benefit-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    /* Pricing adjustments */
    .pricing-header h3 {
        font-size: var(--font-size-lg);
    }
    
    .currency {
        font-size: var(--font-size-lg);
    }
    
    .amount {
        font-size: var(--font-size-2xl);
    }
    
    /* Single column pricing features for small mobile */
    .single-pricing-card .features-list {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }

    /* Features grid single column for very small screens */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
        max-width: 320px;
        margin: 0 auto;
    }

    .feature-card:nth-child(9) {
        grid-column: 1;
        max-width: none;
        margin: 0;
    }

    /* Further optimize screenshot containers for small mobile */
    .screenshot-item .screenshot-placeholder {
        padding: 0 !important;
        aspect-ratio: 16/9;
        height: auto;
        min-height: auto !important;
        max-height: 200px;
    }

    .screenshot-item {
        padding: var(--space-3);
        gap: var(--space-3);
    }

    .image-loader {
        min-height: auto !important;
        height: 100%;
    }
    
    /* Button adjustments */
    .cta-button {
        padding: var(--space-3) var(--space-5);
        font-size: var(--font-size-sm);
        width: 100%;
        max-width: 280px;
    }
    
    .cta-button.primary {
        padding: var(--space-4) var(--space-6);
        font-size: var(--font-size-base);
    }
    
    .cta-button.large {
        font-size: var(--font-size-lg);
        padding: var(--space-4) var(--space-8);
    }

    /* Enhanced Small Mobile Integrations (480px and below) */
    .hero-integrations {
        margin-top: var(--space-6);
    }

    .integrations-text {
        font-size: var(--font-size-xs);
        margin-bottom: var(--space-4);
    }

    .integrations-logos {
        gap: var(--space-3);
    }

    .integration-logo {
        height: 48px;
    }
}

/* Very Small Mobile Screens (360px and below) */
@media (max-width: 360px) {
    .hero-integrations {
        margin-top: var(--space-5);
    }

    .integrations-text {
        font-size: var(--font-size-xs);
        margin-bottom: var(--space-3);
    }

    .integrations-logos {
        gap: var(--space-2);
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        justify-items: center;
        max-width: 280px;
        margin: 0 auto;
    }

    .integration-logo {
        height: 36px;
        min-height: 44px; /* Ensure adequate touch targets */
        padding: var(--space-1);
        transition: all 0.3s ease;
    }

    .integration-logo:active {
        transform: scale(0.95);
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .benefit-card:hover,
    .feature-card:hover,
    .pricing-card:hover,
    .screenshot-item:hover,
    .faq-item:hover,
    .nav-menu a:hover,
    .cta-button:hover {
        transform: none;
        box-shadow: inherit;
    }
    
    /* Make touch targets larger */
    .faq-question,
    .toggle-option,
    .cta-button {
        min-height: 44px;
    }
}
