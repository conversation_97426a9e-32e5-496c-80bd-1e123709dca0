<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- Trackdesk tracker begin -->
  <script async src="//cdn.trackdesk.com/tracking.js"></script> 
  <script>
  (function(t,d,k){(t[k]=t[k]||[]).push(d);t[d]=t[d]||t[k].f||function(){(t[d].q=t[d].q||[]).push(arguments)}})(window,"trackdesk","TrackdeskObject"); 
  trackdesk('seller-synapse', 'click');
  </script>
  <!-- Trackdesk tracker end -->
    <title>Setup Complete - CloseIQ</title>
    <link rel="icon" type="image/png" href="../public/favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Colors from main site */
            --primary-color: #00a8ff;
            --primary-dark: #0084c7;
            --secondary-color: #e100ff;
            --success-color: #10b981;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --bg-dark: #050914;
            --bg-darker: #030712;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #00a8ff 0%, #e100ff 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            
            /* Typography */
            --font-family: 'Inter', sans-serif;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            --font-size-5xl: 3rem;
            
            /* Spacing */
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Border Radius */
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            
            /* Shadows */
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--white);
            background: 
                radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
                radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
                var(--bg-dark);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        /* Header */
        .header {
            background: var(--bg-dark);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--space-4) 0;
        }

        .header-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo img {
            height: 40px;
            width: auto;
            max-width: 200px;
            object-fit: contain;
        }

        /* Step Indicator */
        .step-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--space-16);
            gap: var(--space-4);
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-2);
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: var(--white);
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
        }

        .step.completed .step-number {
            background: var(--success-color);
            color: var(--white);
        }

        .step-label {
            font-size: var(--font-size-sm);
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
            text-align: center;
        }

        .step.active .step-label,
        .step.completed .step-label {
            color: var(--white);
            font-weight: 600;
        }

        .step-connector {
            width: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 1px;
        }

        .step-connector.completed {
            background: var(--success-color);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--space-20) 0;
        }

        .success-container {
            text-align: center;
            max-width: 600px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-xl);
            padding: var(--space-16);
            backdrop-filter: blur(10px);
        }

        .success-icon {
            font-size: var(--font-size-5xl);
            color: var(--success-color);
            margin-bottom: var(--space-6);
            animation: checkmark 0.8s ease-in-out;
        }

        @keyframes checkmark {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .success-title {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            margin-bottom: var(--space-6);
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .success-subtitle {
            font-size: var(--font-size-lg);
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: var(--space-12);
            line-height: 1.6;
        }

        .next-steps {
            text-align: left;
            margin: var(--space-12) 0;
        }

        .next-steps-title {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--white);
            margin-bottom: var(--space-6);
            text-align: center;
        }

        .steps-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            gap: var(--space-4);
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--border-radius-lg);
        }

        .step-number {
            background: var(--success-color);
            color: var(--white);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: var(--font-size-sm);
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            color: var(--white);
            margin-bottom: var(--space-2);
        }

        .step-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--font-size-sm);
            line-height: 1.5;
        }

        .support-section {
            background: rgba(0, 168, 255, 0.1);
            border: 1px solid rgba(0, 168, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: var(--space-6);
            margin: var(--space-12) 0;
        }

        .support-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--white);
            margin-bottom: var(--space-3);
        }

        .support-text {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: var(--space-3);
        }

        .support-email {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .support-email:hover {
            text-decoration: underline;
        }

        .cta-section {
            margin-top: var(--space-12);
        }

        .return-button {
            background: var(--gradient-primary);
            color: var(--white);
            border: none;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            font-size: var(--font-size-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            text-decoration: none;
            display: inline-block;
        }

        .return-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .return-button i {
            margin-right: var(--space-2);
        }

        /* Animated Background Elements */
        .success-container::before {
            content: '';
            position: absolute;
            top: -50px;
            left: -50px;
            right: -50px;
            bottom: -50px;
            background: 
                radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 168, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(225, 0, 255, 0.2) 0%, transparent 50%);
            border-radius: var(--border-radius-xl);
            z-index: -1;
            animation: backgroundPulse 4s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% {
                opacity: 0.5;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.02);
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-3);
            }

            .main-content {
                padding: var(--space-12) 0;
            }

            .success-container {
                padding: var(--space-12);
            }

            .success-title {
                font-size: var(--font-size-3xl);
            }

            .success-subtitle {
                font-size: var(--font-size-base);
            }

            .step-item {
                padding: var(--space-3);
                gap: var(--space-3);
            }

            .step-number {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .return-button {
                padding: var(--space-3) var(--space-6);
                font-size: var(--font-size-base);
                width: 100%;
            }

            .step-indicator {
                gap: var(--space-2);
                margin-bottom: var(--space-12);
            }

            .step-connector {
                width: 40px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: var(--font-size-sm);
            }

            .step-label {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .success-title {
                font-size: var(--font-size-2xl);
            }

            .success-container {
                padding: var(--space-8);
            }

            .success-icon {
                font-size: var(--font-size-4xl);
            }

            .step-indicator {
                flex-direction: column;
                gap: var(--space-3);
            }

            .step-connector {
                width: 2px;
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="../public/closelogo.png" alt="CloseIQ Logo">
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-label">Free Trial Signup</div>
                </div>
                <div class="step-connector completed"></div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-label">Account Setup</div>
                </div>
                <div class="step-connector completed"></div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <div class="step-label">Welcome</div>
                </div>
            </div>

            <div class="success-container">
                <!-- Success Icon -->
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>

                <!-- Success Message -->
                <h1 class="success-title">Step 3 of 3: Welcome to CloseIQ!</h1>
                <p class="success-subtitle">
                    Your account setup is complete! We're excited to help you transform your lead generation with automated PPC audits.
                </p>

                <!-- Next Steps -->
                <div class="next-steps">
                    <h2 class="next-steps-title">What happens next?</h2>
                    <ul class="steps-list">
                        <li class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <div class="step-title">Slack workspace invitation coming soon</div>
                                <div class="step-description">
                                    Within the next few minutes, you'll receive an invitation to join our dedicated Slack workspace for personalized support and updates. We'll also connect with you via Slack Connect for direct communication.
                                </div>
                            </div>
                        </li>
                        <li class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <div class="step-title">Your white-labeled platform is being created</div>
                                <div class="step-description">
                                    We have all your information from the setup form and our team is now customizing your platform with your branding and integrating your calendar and webhooks. This will be completed within 24 hours.
                                </div>
                            </div>
                        </li>
                        <li class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <div class="step-title">Your 7-day trial is protected</div>
                                <div class="step-description">
                                    If there are any delays in setup, we'll automatically extend your trial end date to ensure you get a full 7 days to experience CloseIQ before any charges occur.
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Support Section -->
                <div class="support-section">
                    <h3 class="support-title">Questions? We're here to help!</h3>
                    <p class="support-text">
                        Our team is standing by to ensure you get the most out of CloseIQ. Once you join our Slack workspace, you'll have direct access to our support team for real-time assistance with setup, features, and getting started.
                    </p>
                    <a href="mailto:<EMAIL>" class="support-email">
                        <EMAIL>
                    </a>
                </div>

                <!-- CTA Section -->
                <div class="cta-section">
                    <a href="../index.html" class="return-button">
                        <i class="fas fa-home"></i>
                        Return to CloseIQ
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate step items on load
            const stepItems = document.querySelectorAll('.step-item');
            stepItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.6s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 300 + (index * 200));
            });

            // Add hover effects to step items
            stepItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.2)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });

            console.log('CloseIQ Success page loaded');
        });
    </script>
</body>
</html>
