<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to CloseIQ - Onboarding</title>
    <link rel="icon" type="image/png" href="../public/favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Colors from main site */
            --primary-color: #00a8ff;
            --primary-dark: #0084c7;
            --secondary-color: #e100ff;
            --success-color: #10b981;
            --error-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --bg-dark: #050914;
            --bg-darker: #030712;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #00a8ff 0%, #e100ff 100%);
            
            /* Typography */
            --font-family: 'Inter', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            
            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Border Radius */
            --border-radius-sm: 0.375rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--white);
            background: 
                radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
                radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
                var(--bg-dark);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        /* Header */
        .header {
            background: var(--bg-dark);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--space-4) 0;
        }

        .header-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo img {
            height: 40px;
            width: auto;
            max-width: 200px;
            object-fit: contain;
        }

        /* Main Content */
        .main-content {
            padding: var(--space-12) 0;
        }

        .page-header {
            text-align: center;
            margin-bottom: var(--space-12);
        }

        .page-title {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            margin-bottom: var(--space-4);
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: var(--font-size-lg);
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Step Indicator */
        .step-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--space-12);
            gap: var(--space-4);
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-2);
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: linear-gradient(135deg, #00a8ff 0%, #e100ff 100%);
            color: var(--white);
            box-shadow: 0 0 20px rgba(0, 168, 255, 0.5);
        }

        .step.completed .step-number {
            background: var(--success-color);
            color: var(--white);
        }

        .step-label {
            font-size: var(--font-size-sm);
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
            text-align: center;
        }

        .step.active .step-label,
        .step.completed .step-label {
            color: var(--white);
            font-weight: 600;
        }

        .step-connector {
            width: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 1px;
        }

        .step-connector.completed {
            background: var(--success-color);
        }

        /* Form Styles */
        .onboarding-form {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-xl);
            padding: var(--space-8);
            backdrop-filter: blur(10px);
        }

        .form-section {
            margin-bottom: var(--space-10);
        }

        .form-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--white);
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-3);
            border-bottom: 2px solid rgba(0, 168, 255, 0.3);
        }

        .form-group {
            margin-bottom: var(--space-5);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--white);
            margin-bottom: var(--space-2);
            font-size: var(--font-size-sm);
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-family: var(--font-family);
            background: var(--white);
            color: var(--text-dark);
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 168, 255, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-checkbox {
            display: flex;
            align-items: flex-start;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .form-checkbox input[type="checkbox"] {
            margin-top: 2px;
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .checkbox-label {
            font-size: var(--font-size-sm);
            color: var(--white);
            cursor: pointer;
        }

        .instructions-box {
            background: rgba(0, 168, 255, 0.1);
            border: 1px solid rgba(0, 168, 255, 0.3);
            border-radius: var(--border-radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-4);
        }

        .instructions-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--space-3);
            font-size: var(--font-size-sm);
        }

        .instructions-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .instructions-list li {
            margin-bottom: var(--space-2);
            font-size: var(--font-size-sm);
            color: rgba(255, 255, 255, 0.9);
            position: relative;
            padding-left: var(--space-6);
        }

        .instructions-list li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background: var(--primary-color);
            color: var(--white);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        .instructions-list {
            counter-reset: step-counter;
        }

        .conditional-field {
            display: none;
            margin-top: var(--space-4);
        }

        .conditional-field.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .instruction-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--font-size-sm);
            margin-bottom: var(--space-3);
            line-height: 1.5;
        }

        .instruction-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .instruction-link:hover {
            text-decoration: underline;
        }

        /* Error States */
        .error {
            border-color: var(--error-color) !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }

        .error-message {
            color: var(--error-color);
            font-size: var(--font-size-sm);
            margin-top: var(--space-1);
            display: none;
        }

        .error-message.show {
            display: block;
        }

        /* Submit Button */
        .submit-section {
            margin-top: var(--space-12);
            text-align: center;
        }

        .submit-button {
            background: var(--gradient-primary);
            color: var(--white);
            border: none;
            padding: var(--space-4) var(--space-10);
            border-radius: var(--border-radius-lg);
            font-weight: 700;
            font-size: var(--font-size-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            min-width: 200px;
        }

        .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid var(--white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: var(--space-2);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .submit-button.loading .loading-spinner {
            display: inline-block;
        }

        .submit-button.loading .button-text {
            opacity: 0.7;
        }

        /* Success Message */
        .success-message {
            display: none;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: var(--border-radius-lg);
            padding: var(--space-6);
            margin-top: var(--space-6);
            text-align: center;
        }

        .success-message.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .success-icon {
            font-size: var(--font-size-2xl);
            color: var(--success-color);
            margin-bottom: var(--space-3);
        }

        .success-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--white);
            margin-bottom: var(--space-2);
        }

        .success-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--font-size-sm);
        }

        /* Form Support Section */
        .form-support-section {
            background: rgba(0, 168, 255, 0.1);
            border: 1px solid rgba(0, 168, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: var(--space-6);
            margin: var(--space-8) 0;
            text-align: center;
        }

        .form-support-section .support-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--white);
            margin-bottom: var(--space-3);
        }

        .form-support-section .support-text {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: var(--space-4);
            line-height: 1.6;
        }

        .form-support-section .support-email {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            font-size: var(--font-size-lg);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-4);
            border: 1px solid rgba(0, 168, 255, 0.3);
            border-radius: var(--border-radius-md);
            background: rgba(0, 168, 255, 0.05);
            transition: all 0.3s ease;
        }

        .form-support-section .support-email:hover {
            background: rgba(0, 168, 255, 0.1);
            border-color: rgba(0, 168, 255, 0.5);
            transform: translateY(-1px);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-3);
            }

            .main-content {
                padding: var(--space-8) 0;
            }

            .page-title {
                font-size: var(--font-size-3xl);
            }

            .page-subtitle {
                font-size: var(--font-size-base);
            }

            .onboarding-form {
                padding: var(--space-6);
            }

            .section-title {
                font-size: var(--font-size-lg);
            }

            .form-input,
            .form-select,
            .form-textarea {
                padding: var(--space-3);
            }

            .submit-button {
                padding: var(--space-3) var(--space-8);
                font-size: var(--font-size-base);
                width: 100%;
            }

            .step-indicator {
                gap: var(--space-2);
                margin-bottom: var(--space-8);
            }

            .step-connector {
                width: 40px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: var(--font-size-sm);
            }

            .step-label {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: var(--font-size-2xl);
            }

            .onboarding-form {
                padding: var(--space-4);
            }

            .instructions-list li {
                font-size: var(--font-size-xs);
            }

            .step-indicator {
                flex-direction: column;
                gap: var(--space-3);
            }

            .step-connector {
                width: 2px;
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="../public/closelogo.png" alt="CloseIQ Logo">
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-label">Secure Payment</div>
                </div>
                <div class="step-connector completed"></div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <div class="step-label">Account Setup</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-label">Welcome</div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Step 2 of 3: Welcome to CloseIQ!</h1>
                <p class="page-subtitle">
                    We just need a few quick things from you and we'll get your customized white label account setup.
                </p>
            </div>

            <!-- Hidden iframe for form submission -->
            <iframe name="hidden-form-target" style="display: none;"></iframe>
            
            <!-- Onboarding Form -->
            <form id="onboarding-form" action="https://script.google.com/macros/s/AKfycbwxt2_fWKC2f13wQ_EI42aR3-UisA5JBS6isaNm-9OGKwzO35JRlTMEiTZM9j_w88Dm/exec" method="POST" target="hidden-form-target" class="onboarding-form">
                <!-- Section 1: Basic Information -->
                <div class="form-section">
                    <h2 class="section-title">Basic Information</h2>
                    
                    <div class="form-group">
                        <label for="companyName" class="form-label">Company Name *</label>
                        <input type="text" id="companyName" name="Company_Name" class="form-input" placeholder="Company Name" required>
                        <div class="error-message">Please enter your company name</div>
                    </div>
                </div>

                <!-- Section 2: Logo & Branding -->
                <div class="form-section">
                    <h2 class="section-title">Logo & Branding</h2>
                    
                    <div class="form-group">
                        <label for="website" class="form-label">Your agency/company website URL *</label>
                        <p class="instruction-text">
                            We'll automatically extract your logo and brand colors from your website
                        </p>
                        <input type="text" id="website" name="Website_URL" class="form-input" placeholder="youragency.com or www.youragency.com" required>
                        <div class="error-message">Please enter a valid website URL</div>
                    </div>
                </div>

                <!-- Section 3: Calendar Integration -->
                <div class="form-section">
                    <h2 class="section-title">Calendar Integration</h2>
                    
                    <div class="form-group">
                        <label for="calendarSystem" class="form-label">What calendar system do you use? *</label>
                        <select id="calendarSystem" name="Calendar_System" class="form-select" required>
                            <option value="">Select your calendar</option>
                            <option value="tidycal">TidyCal</option>
                            <option value="calendly">Calendly</option>
                            <option value="gohighlevel">GoHighLevel</option>
                            <option value="zoom">Zoom Scheduler</option>
                            <option value="other">Other</option>
                        </select>
                        <div class="error-message">Please select your calendar system</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="calendarEmbed" class="form-label">Please provide your calendar embed code *</label>
                        <p class="instruction-text">
                            Copy the embed code from your calendar app's integration/embed settings and paste it below.
                        </p>
                        
                        <!-- Dynamic Calendar Instructions -->
                        <div class="instructions-box" id="calendlyInstructions">
                            <div class="instructions-title">How to Get Embed Code from Calendly:</div>
                            <ol class="instructions-list">
                                <li><strong>Log into Calendly</strong> - Go to calendly.com and sign into your account</li>
                                <li><strong>Select Your Event</strong> - Click on the event type you want to embed (e.g., "30 Minute Meeting")</li>
                                <li><strong>Get Embed Code</strong> - Click the "Share" button (top right of your event page), Select "Add to Website" from the dropdown menu</li>
                                <li><strong>Choose Embed Type</strong> - Select "Inline Embed" (recommended for CloseIQ), You'll see a preview of how it will look</li>
                                <li><strong>Copy the Code</strong> - Copy the embed code and paste it below</li>
                            </ol>
                        </div>

                        <div class="instructions-box" id="tidycalInstructions" style="display: none;">
                            <div class="instructions-title">How to Get Embed Code from TidyCal:</div>
                            <ol class="instructions-list">
                                <li><strong>Access Dashboard</strong> - Log into your TidyCal account and go to your dashboard</li>
                                <li><strong>Select Event Type</strong> - Choose the event/meeting type you want to embed</li>
                                <li><strong>Share Settings</strong> - Click on "Share" or "Embed" options for that event</li>
                                <li><strong>Get Embed Code</strong> - Look for "Embed Code" or "Website Integration" option</li>
                                <li><strong>Copy Code</strong> - Copy the iframe or embed code provided and paste it below</li>
                            </ol>
                        </div>

                        <div class="instructions-box" id="gohighlevelInstructions" style="display: none;">
                            <div class="instructions-title">How to Get Embed Code from GoHighLevel:</div>
                            <ol class="instructions-list">
                                <li><strong>Access Calendar</strong> - Go to your GoHighLevel dashboard and navigate to Calendar</li>
                                <li><strong>Select Calendar</strong> - Choose the calendar you want to embed</li>
                                <li><strong>Calendar Settings</strong> - Click on settings or sharing options for that calendar</li>
                                <li><strong>Get Embed Code</strong> - Look for "Embed" or "Website Integration" in the sharing options</li>
                                <li><strong>Copy Code</strong> - Copy the provided embed code and paste it below</li>
                            </ol>
                        </div>

                        <div class="instructions-box" id="zoomInstructions" style="display: none;">
                            <div class="instructions-title">How to Get Embed Code from Zoom Scheduler:</div>
                            <ol class="instructions-list">
                                <li><strong>Access Zoom Web Portal</strong> - Log into zoom.us and go to your web portal</li>
                                <li><strong>Navigate to Scheduler</strong> - Go to Personal > Scheduler or find the Zoom Scheduler section</li>
                                <li><strong>Select Meeting Type</strong> - Choose the meeting type you want to embed</li>
                                <li><strong>Get Sharing Link</strong> - Look for "Share" or "Embed" options in the scheduler settings</li>
                                <li><strong>Copy Code</strong> - Copy the embed code or iframe code and paste it below</li>
                            </ol>
                        </div>

                        <div class="instructions-box" id="otherCalendarInstructions" style="display: none;">
                            <div class="instructions-title">How to Get Embed Code from Your Calendar System:</div>
                            <ol class="instructions-list">
                                <li><strong>Access Settings</strong> - Go to your calendar platform's settings or admin area</li>
                                <li><strong>Find Integration Options</strong> - Look for "Embed", "Widget", or "Integration" settings</li>
                                <li><strong>Generate Embed Code</strong> - Create or find the HTML embed code for your calendar</li>
                                <li><strong>Configure Display</strong> - Choose inline or widget display options if available</li>
                                <li><strong>Copy the Code</strong> - Copy the generated embed code and paste it below</li>
                            </ol>
                        </div>
                        
                        <textarea id="calendarEmbed" name="Calendar_Embed_Code" class="form-textarea" placeholder="Paste your calendar embed code here" rows="6" required></textarea>
                        <div class="error-message">Please provide your calendar embed code</div>
                    </div>
                </div>

                <!-- Section 4: Slack Integration -->
                <div class="form-section">
                    <h2 class="section-title">Slack Integration</h2>
                    
                    <div class="form-checkbox">
                        <input type="hidden" name="Slack_Enabled" value="false">
                        <input type="checkbox" id="slackEnabled" name="Slack_Enabled" value="true">
                        <label for="slackEnabled" class="checkbox-label">I want Slack notifications when new leads come in</label>
                    </div>
                    
                    <div class="conditional-field" id="slackWebhookField">
                        <div class="form-group">
                            <label for="slackWebhook" class="form-label">Slack Webhook URL</label>
                            <p class="instruction-text">
                                Simple 3 step instructions on how to get it, <a href="#" class="instruction-link">HERE</a><br>
                                Once you have the URL just paste it below.
                            </p>
                            <input type="text" id="slackWebhook" name="Slack_Webhook" class="form-input" placeholder="Paste your Slack webhook URL here">
                            <div class="error-message">Please enter a valid Slack webhook URL</div>
                        </div>
                    </div>
                </div>

                <!-- Section 5: CRM Integration -->
                <div class="form-section">
                    <h2 class="section-title">CRM Integration</h2>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="crmEnabled" name="crmEnabled">
                        <label for="crmEnabled" class="checkbox-label">I want to integrate my CRM</label>
                    </div>
                    
                    <div class="conditional-field" id="crmIntegrationField">
                        <div class="form-group">
                            <label for="crmSystem" class="form-label">Which CRM do you use? *</label>
                            <select id="crmSystem" name="CRM_System" class="form-select">
                                <option value="">Select your CRM</option>
                                <option value="gohighlevel">GoHighLevel</option>
                                <option value="hubspot">HubSpot</option>
                                <option value="pipedrive">Pipedrive</option>
                                <option value="salesforce">Salesforce</option>
                                <option value="other">Other</option>
                            </select>
                            <div class="error-message">Please select your CRM system</div>
                        </div>
                        
                        <div class="conditional-field" id="crmWebhookField">
                            <div class="form-group">
                                <label for="crmWebhook" class="form-label">CRM Webhook URL *</label>
                                
                                <!-- Dynamic CRM Instructions -->
                                <div class="instructions-box" id="gohighlevelCrmInstructions">
                                    <div class="instructions-title">How to Get Webhook URL from GoHighLevel:</div>
                                    <ol class="instructions-list">
                                        <li><strong>Access Settings</strong> - Go to your GoHighLevel dashboard and navigate to Settings</li>
                                        <li><strong>Find Webhooks</strong> - Look for "Webhooks" or "Integrations" in the settings menu</li>
                                        <li><strong>Create Webhook</strong> - Click "Add Webhook" or "Create New Webhook"</li>
                                        <li><strong>Configure Events</strong> - Select the events you want to trigger the webhook</li>
                                        <li><strong>Copy URL</strong> - Copy the generated webhook URL and paste it below</li>
                                    </ol>
                                </div>

                                <div class="instructions-box" id="hubspotCrmInstructions" style="display: none;">
                                    <div class="instructions-title">How to Get Webhook URL from HubSpot:</div>
                                    <ol class="instructions-list">
                                        <li><strong>Access Workflows</strong> - Go to your HubSpot dashboard and navigate to Automation > Workflows</li>
                                        <li><strong>Create or Edit Workflow</strong> - Click "Create workflow" or edit an existing contact-based workflow</li>
                                        <li><strong>Add Webhook Action</strong> - Click the + icon to add an action, then select "Send a webhook" from the Data ops section</li>
                                        <li><strong>Configure Webhook</strong> - Click Method dropdown and select POST, then enter your webhook URL (must start with HTTPS)</li>
                                        <li><strong>Copy URL</strong> - The webhook URL you enter here is what you'll paste in CloseIQ</li>
                                    </ol>
                                </div>

                                <div class="instructions-box" id="pipedriveCrmInstructions" style="display: none;">
                                    <div class="instructions-title">How to Get Webhook URL from Pipedrive:</div>
                                    <ol class="instructions-list">
                                        <li><strong>Access Settings</strong> - Go to your Pipedrive dashboard and navigate to Settings > Tools and apps</li>
                                        <li><strong>Find Webhooks</strong> - Look for "Webhooks" or "Integrations" in the settings menu</li>
                                        <li><strong>Create Webhook</strong> - Click "Create new webhook" or "+ Webhooks"</li>
                                        <li><strong>Configure Events</strong> - Select the events you want to trigger the webhook (e.g., "added" and "deal")</li>
                                        <li><strong>Copy URL</strong> - Copy the generated webhook URL and paste it below</li>
                                    </ol>
                                </div>

                                <div class="instructions-box" id="salesforceCrmInstructions" style="display: none;">
                                    <div class="instructions-title">How to Get Webhook URL from Salesforce:</div>
                                    <ol class="instructions-list">
                                        <li><strong>Create Site</strong> - Go to Setup > Sites and Domains > Sites, then create a new site or use existing</li>
                                        <li><strong>Create Apex Class</strong> - Create a REST API class with @RestResource annotation and @HttpPost method</li>
                                        <li><strong>Enable Public Access</strong> - In site settings, go to "Public Access Settings" and enable the Apex class</li>
                                        <li><strong>Build URL</strong> - Combine your site domain + "/services/apexrest/" + your URL mapping</li>
                                        <li><strong>Copy URL</strong> - Use the complete URL (e.g., https://yoursite.force.com/services/apexrest/webhook)</li>
                                    </ol>
                                </div>

                                <div class="instructions-box" id="otherCrmInstructions" style="display: none;">
                                    <div class="instructions-title">How to Get Webhook URL from Your CRM:</div>
                                    <ol class="instructions-list">
                                        <li><strong>Access Settings</strong> - Go to your CRM's settings or admin area</li>
                                        <li><strong>Find Integrations</strong> - Look for "Webhooks", "API", or "Integrations" section</li>
                                        <li><strong>Create Webhook</strong> - Set up a new webhook or outbound integration</li>
                                        <li><strong>Configure Events</strong> - Select which events should trigger data sending</li>
                                        <li><strong>Copy URL</strong> - Copy the webhook endpoint URL and paste it below</li>
                                    </ol>
                                </div>
                                
                                <input type="text" id="crmWebhook" name="CRM_Webhook" class="form-input" placeholder="Paste your CRM webhook URL here">
                                <div class="error-message">Please enter a valid CRM webhook URL</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Section -->
                <div class="submit-section">
                    <button type="submit" class="submit-button" id="submitButton">
                        <span class="loading-spinner"></span>
                        <span class="button-text">Complete Setup</span>
                    </button>
                    
                    <!-- Support Section -->
                    <div class="form-support-section">
                        <h3 class="support-title">Need Help?</h3>
                        <p class="support-text">
                            Having trouble with any of these integrations? Our team is here to help you get everything set up properly.
                        </p>
                        <a href="mailto:<EMAIL>" class="support-email">
                            <i class="fas fa-envelope"></i>
                            <EMAIL>
                        </a>
                    </div>
                    
                    <div class="success-message" id="successMessage">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="success-title">Setup Complete!</div>
                        <div class="success-text">
                            Redirecting you to the next step...
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <script>
        // Form handling and validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('onboarding-form');
            const submitButton = document.getElementById('submitButton');
            const successMessage = document.getElementById('successMessage');
            
            // Conditional field handling
            const slackCheckbox = document.getElementById('slackEnabled');
            const slackWebhookField = document.getElementById('slackWebhookField');
            const crmCheckbox = document.getElementById('crmEnabled');
            const crmIntegrationField = document.getElementById('crmIntegrationField');
            const crmSelect = document.getElementById('crmSystem');
            const crmWebhookField = document.getElementById('crmWebhookField');
            const calendarSelect = document.getElementById('calendarSystem');
            
            // Show/hide Slack webhook field
            slackCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    slackWebhookField.classList.add('show');
                    document.getElementById('slackWebhook').required = true;
                } else {
                    slackWebhookField.classList.remove('show');
                    document.getElementById('slackWebhook').required = false;
                    clearError('slackWebhook');
                }
            });
            
            // Show/hide CRM integration section
            crmCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    crmIntegrationField.classList.add('show');
                    document.getElementById('crmSystem').required = true;
                } else {
                    crmIntegrationField.classList.remove('show');
                    crmWebhookField.classList.remove('show');
                    document.getElementById('crmSystem').required = false;
                    document.getElementById('crmWebhook').required = false;
                    clearError('crmSystem');
                    clearError('crmWebhook');
                }
            });
            
            // Show/hide CRM webhook field and instructions
            crmSelect.addEventListener('change', function() {
                if (this.value && this.value !== '') {
                    crmWebhookField.classList.add('show');
                    document.getElementById('crmWebhook').required = true;
                    showCrmInstructions(this.value);
                } else {
                    crmWebhookField.classList.remove('show');
                    document.getElementById('crmWebhook').required = false;
                    clearError('crmWebhook');
                }
            });

            // Show/hide calendar instructions
            calendarSelect.addEventListener('change', function() {
                showCalendarInstructions(this.value);
            });

            // Function to show appropriate calendar instructions
            function showCalendarInstructions(calendarType) {
                // Hide all calendar instruction boxes
                const calendarInstructions = [
                    'calendlyInstructions',
                    'tidycalInstructions', 
                    'gohighlevelInstructions',
                    'zoomInstructions',
                    'otherCalendarInstructions'
                ];
                
                calendarInstructions.forEach(id => {
                    document.getElementById(id).style.display = 'none';
                });

                // Show appropriate instructions based on selection
                switch(calendarType) {
                    case 'calendly':
                        document.getElementById('calendlyInstructions').style.display = 'block';
                        break;
                    case 'tidycal':
                        document.getElementById('tidycalInstructions').style.display = 'block';
                        break;
                    case 'gohighlevel':
                        document.getElementById('gohighlevelInstructions').style.display = 'block';
                        break;
                    case 'zoom':
                        document.getElementById('zoomInstructions').style.display = 'block';
                        break;
                    case 'other':
                        document.getElementById('otherCalendarInstructions').style.display = 'block';
                        break;
                    default:
                        document.getElementById('calendlyInstructions').style.display = 'block';
                        break;
                }
            }

            // Function to show appropriate CRM instructions
            function showCrmInstructions(crmType) {
                // Hide all CRM instruction boxes
                const crmInstructions = [
                    'gohighlevelCrmInstructions',
                    'hubspotCrmInstructions',
                    'pipedriveCrmInstructions',
                    'salesforceCrmInstructions',
                    'otherCrmInstructions'
                ];
                
                crmInstructions.forEach(id => {
                    document.getElementById(id).style.display = 'none';
                });

                // Show appropriate instructions based on selection
                switch(crmType) {
                    case 'gohighlevel':
                        document.getElementById('gohighlevelCrmInstructions').style.display = 'block';
                        break;
                    case 'hubspot':
                        document.getElementById('hubspotCrmInstructions').style.display = 'block';
                        break;
                    case 'pipedrive':
                        document.getElementById('pipedriveCrmInstructions').style.display = 'block';
                        break;
                    case 'salesforce':
                        document.getElementById('salesforceCrmInstructions').style.display = 'block';
                        break;
                    case 'other':
                        document.getElementById('otherCrmInstructions').style.display = 'block';
                        break;
                    default:
                        document.getElementById('gohighlevelCrmInstructions').style.display = 'block';
                        break;
                }
            }
            
            // Real-time validation
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
                
                input.addEventListener('input', function() {
                    if (this.classList.contains('error')) {
                        validateField(this);
                    }
                });
            });
            
            // Handle form submission with iframe and redirect
            form.addEventListener('submit', function(e) {
                // Validate form before submission
                if (!validateForm()) {
                    e.preventDefault();
                    return false;
                }
                
                // Show loading state
                submitButton.disabled = true;
                submitButton.classList.add('loading');
                
                console.log('Form submitting to Google Apps Script via hidden iframe...');
                
                // Let form submit to hidden iframe (no preventDefault needed)
                // Set up redirect after successful submission
                setTimeout(() => {
                    // Show success message
                    successMessage.classList.add('show');
                    
                    // Redirect to success page after brief delay
                    setTimeout(() => {
                        console.log('Redirecting to success page...');
                        window.location.href = 'https://closeiq.sellersynapse.com/onboarding/success';
                    }, 2000);
                }, 1000); // Wait 1 second for form to submit, then show success and redirect
            });
            
            // Listen for iframe load to detect form submission completion
            const hiddenIframe = document.querySelector('iframe[name="hidden-form-target"]');
            if (hiddenIframe) {
                hiddenIframe.addEventListener('load', function() {
                    console.log('Form submission completed via iframe');
                    // Form has been successfully submitted to Google Apps Script
                    // The setTimeout above will handle showing success message and redirect
                });
            }
            
            function validateField(field) {
                const value = field.value.trim();
                const isRequired = field.required;
                let isValid = true;
                let errorMessage = '';
                
                // Only validate required fields - no URL validation
                if (isRequired && !value) {
                    isValid = false;
                    errorMessage = `Please ${field.tagName === 'SELECT' ? 'make a selection' : 'fill in this field'}`;
                }
                
                // Email validation only for email fields
                else if (field.type === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                }
                
                // No other validation - accept any text input
                
                if (isValid) {
                    clearError(field.id);
                } else {
                    showError(field.id, errorMessage);
                }
                
                return isValid;
            }
            
            function validateForm() {
                let isValid = true;
                
                inputs.forEach(input => {
                    if (!validateField(input)) {
                        isValid = false;
                    }
                });
                
                return isValid;
            }
            
            function showError(fieldId, message) {
                const field = document.getElementById(fieldId);
                const errorMessage = field.parentNode.querySelector('.error-message');
                
                field.classList.add('error');
                if (errorMessage) {
                    errorMessage.textContent = message;
                    errorMessage.classList.add('show');
                }
            }
            
            function clearError(fieldId) {
                const field = document.getElementById(fieldId);
                const errorMessage = field.parentNode.querySelector('.error-message');
                
                field.classList.remove('error');
                if (errorMessage) {
                    errorMessage.classList.remove('show');
                }
            }
            
            console.log('CloseIQ Onboarding form loaded');
        });
    </script>
</body>
</html>
